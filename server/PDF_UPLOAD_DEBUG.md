# PDF Upload Debug Guide

## Overview

This guide helps debug PDF upload issues in the rental advance system. The system now has comprehensive logging and alternative upload methods.

## Debug Steps

### 1. Test PDF Upload Endpoint

Use the debug endpoint to test PDF uploads:

```bash
# Test with a specific PDF file
node test-pdf-upload.js /path/to/your/contract.pdf

# Test with auto-generated PDF
node test-pdf-upload.js
```

### 2. Check Logs

Monitor the server logs for detailed information:

```bash
# In the server directory
npm run start:dev

# Look for these log entries:
# - "Arquivo recebido:" (Controller)
# - "Buffer start:" (DriveService)
# - "Arquivo salvo temporariamente em:" (DriveService)
# - "Arquivo no Drive - Tamanho original:" (DriveService)
```

### 3. Environment Variables

Configure upload method:

```bash
# Use direct Google Drive upload (default)
USE_N8N_FILE_UPLOAD=false

# Use N8N webhook for file upload (alternative)
USE_N8N_FILE_UPLOAD=true
```

### 4. Manual File Inspection

The system saves temporary copies of uploaded files in `/tmp/` for manual inspection:

```bash
# Check temporary files
ls -la /tmp/debug-*

# Verify PDF content
file /tmp/debug-*.pdf
```

## Common Issues and Solutions

### Issue 1: Empty Files in Google Drive

**Symptoms:**
- Files appear in Google Drive but are 0 bytes
- Log shows "Tamanho no Drive: 0 bytes"

**Possible Causes:**
- Stream creation issue
- Google Drive API problem
- Buffer corruption

**Solution:**
1. Check if temporary file is correct: `file /tmp/debug-*.pdf`
2. If temp file is good, switch to N8N upload: `USE_N8N_FILE_UPLOAD=true`
3. Check Google Drive API credentials

### Issue 2: Frontend Not Sending File Correctly

**Symptoms:**
- "hasBuffer: false" in logs
- "bufferLength: 0" in logs

**Possible Causes:**
- FormData not constructed properly
- File input not working
- Multer configuration issue

**Solution:**
1. Check browser network tab for request payload
2. Verify file is selected in frontend
3. Test with debug endpoint directly

### Issue 3: PDF Validation Fails

**Symptoms:**
- "Buffer não parece ser um PDF válido" warning
- Extraction fails

**Possible Causes:**
- Corrupted file
- Password-protected PDF
- Non-standard PDF format

**Solution:**
1. Test with simple PDF: `node test-pdf-upload.js`
2. Check PDF with: `file your-pdf.pdf`
3. Try different PDF files

## N8N Webhook Alternative

If Google Drive direct upload fails, the system can use N8N webhooks:

### N8N Webhook Endpoints Required:

1. **File Upload**: `POST /upload-file`
   - Receives: multipart/form-data with file, operationId, fileType
   - Returns: `{ fileUrl: "https://drive.google.com/..." }`

2. **Contract Extraction**: `POST /extract-contract` (existing)
   - Receives: multipart/form-data with file, operationId
   - Returns: extracted data or error

### Enable N8N Upload:

```bash
export USE_N8N_FILE_UPLOAD=true
```

## Debugging Checklist

- [ ] Test with debug endpoint
- [ ] Check server logs for file reception
- [ ] Verify temporary file is created correctly
- [ ] Check Google Drive upload logs
- [ ] Test with different PDF files
- [ ] Try N8N upload alternative
- [ ] Verify N8N webhook responses

## Log Analysis

### Successful Upload Logs:
```
Arquivo recebido: { originalname: 'contract.pdf', size: 12345, hasBuffer: true }
Buffer start: %PDF-1.4
Arquivo salvo temporariamente em: /tmp/debug-op123-1234567890.pdf
Arquivo no Drive - Tamanho original: 12345 bytes, Tamanho no Drive: 12345 bytes
```

### Failed Upload Logs:
```
ERRO: Tamanho do arquivo no Drive (0) não corresponde ao tamanho original (12345)
```

## Contact

If issues persist after following this guide, check:
1. Google Drive API quotas
2. N8N webhook availability
3. Network connectivity
4. File permissions in `/tmp/`
