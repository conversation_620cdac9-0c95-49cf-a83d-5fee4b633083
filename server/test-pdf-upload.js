#!/usr/bin/env node

/**
 * Script de teste para verificar o upload de PDF
 * 
 * Uso:
 * node test-pdf-upload.js [caminho-para-pdf]
 * 
 * Se nenhum arquivo for especificado, cria um PDF de teste
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

// Configuração
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const TEST_ENDPOINT = `${BASE_URL}/api/rental-advance/debug/test-pdf-upload`;

// Função para criar um PDF de teste simples
function createTestPDF() {
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Teste de PDF) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`;

  const testPdfPath = path.join(__dirname, 'test-upload.pdf');
  fs.writeFileSync(testPdfPath, pdfContent);
  console.log(`PDF de teste criado em: ${testPdfPath}`);
  return testPdfPath;
}

async function testPdfUpload(pdfPath) {
  try {
    console.log('=== TESTE DE UPLOAD DE PDF ===');
    console.log(`Arquivo: ${pdfPath}`);
    console.log(`Endpoint: ${TEST_ENDPOINT}`);
    
    // Verificar se o arquivo existe
    if (!fs.existsSync(pdfPath)) {
      throw new Error(`Arquivo não encontrado: ${pdfPath}`);
    }

    // Ler o arquivo
    const fileBuffer = fs.readFileSync(pdfPath);
    console.log(`Tamanho do arquivo: ${fileBuffer.length} bytes`);
    
    // Verificar se é um PDF válido
    const bufferStart = fileBuffer.subarray(0, 10).toString();
    console.log(`Início do buffer: ${bufferStart}`);
    console.log(`É PDF válido: ${bufferStart.startsWith('%PDF')}`);

    // Criar FormData
    const formData = new FormData();
    formData.append('testPdf', fileBuffer, {
      filename: path.basename(pdfPath),
      contentType: 'application/pdf'
    });

    console.log('\nEnviando arquivo para o servidor...');
    
    // Fazer upload
    const response = await axios.post(TEST_ENDPOINT, formData, {
      headers: {
        ...formData.getHeaders(),
      },
      timeout: 30000,
    });

    console.log('\n=== RESPOSTA DO SERVIDOR ===');
    console.log(`Status: ${response.status}`);
    console.log('Dados:', JSON.stringify(response.data, null, 2));

    if (response.data.success) {
      console.log('\n✅ Teste concluído com sucesso!');
      
      const data = response.data.data;
      console.log(`\nArquivo temporário: ${data.tempFilePath}`);
      console.log(`URL no Drive: ${data.driveUrl}`);
      console.log(`ID da operação de teste: ${data.testOperationId}`);
      
      // Verificar se o arquivo temporário foi criado corretamente
      if (fs.existsSync(data.tempFilePath)) {
        const tempFileSize = fs.statSync(data.tempFilePath).size;
        console.log(`Tamanho do arquivo temporário: ${tempFileSize} bytes`);
        console.log(`Tamanhos coincidem: ${tempFileSize === fileBuffer.length}`);
      } else {
        console.log('⚠️  Arquivo temporário não encontrado');
      }
    } else {
      console.log('\n❌ Teste falhou');
      console.log('Erro:', response.data.message);
    }

  } catch (error) {
    console.error('\n❌ Erro durante o teste:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Dados:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error(error.message);
    }
  }
}

// Função principal
async function main() {
  const args = process.argv.slice(2);
  let pdfPath;

  if (args.length > 0) {
    pdfPath = args[0];
  } else {
    console.log('Nenhum arquivo especificado, criando PDF de teste...');
    pdfPath = createTestPDF();
  }

  await testPdfUpload(pdfPath);
  
  // Limpar arquivo de teste se foi criado
  if (args.length === 0 && fs.existsSync(pdfPath)) {
    fs.unlinkSync(pdfPath);
    console.log(`\nArquivo de teste removido: ${pdfPath}`);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testPdfUpload, createTestPDF };
