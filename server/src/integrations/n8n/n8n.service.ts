import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { PrismaService } from '../../prisma.service';
import { RentalAdvanceStatus } from '../../rental-advance/enums/rental-status.enum';

@Injectable()
export class N8nService {
  private readonly logger = new Logger(N8nService.name);
  private readonly baseWebhookUrl = process.env.N8N_WEBHOOK_URL;

  constructor(private readonly prisma: PrismaService) {
    if (!this.baseWebhookUrl) {
      this.logger.error('N8N_WEBHOOK_URL não configurada');
    }
  }

  async sendWhatsappCode(phone: string, code: string): Promise<void> {
    if (!this.baseWebhookUrl) {
      throw new InternalServerErrorException('N8N webhook URL não configurada');
    }

    try {
      this.logger.log(
        `Enviando código WhatsApp para: ${phone.substring(0, 4)}****`,
      );

      const response = await axios.post(
        `${this.baseWebhookUrl}/whatsapp-code`,
        {
          phone,
          code,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      if (response.status !== 200) {
        throw new Error(`N8N retornou status ${response.status}`);
      }

      this.logger.log('Código WhatsApp enviado com sucesso');
    } catch (error) {
      this.logger.error('Falha ao enviar código via N8N:', error);
      throw new InternalServerErrorException(
        'Falha ao enviar código de verificação. Tente novamente.', + this.baseWebhookUrl + '/whatsapp-code',
      );
    }
  }

  sendContractForExtraction(
    operationId: string,
    contractPdf: Express.Multer.File,
  ): void {
    if (!this.baseWebhookUrl) {
      this.logger.error('N8N webhook URL não configurada');
      return;
    }

    // Executa de forma assíncrona sem bloquear
    this.processContractExtraction(operationId, contractPdf);
  }

  private async processContractExtraction(
    operationId: string,
    contractPdf: Express.Multer.File,
  ): Promise<void> {
    try {
      this.logger.log(
        `Enviando contrato para extração: ${contractPdf.originalname} (operação: ${operationId})`,
      );

      const formData = new FormData();
      formData.append(
        'file',
        new Blob([contractPdf.buffer]),
        contractPdf.originalname,
      );
      formData.append('mimeType', contractPdf.mimetype);
      formData.append('operationId', operationId);
      formData.append('timestamp', new Date().toISOString());

      const response: AxiosResponse = await axios.post(
        `${this.baseWebhookUrl}/extract-contract`,
        formData,
        {
          timeout: 60000, // 60 segundos para extração
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      if (!response.data) {
        throw new Error('Resposta vazia do N8N');
      }

      // Processar resposta e atualizar banco de dados
      await this.handleExtractionSuccess(operationId, response.data);

    } catch (error) {
      this.logger.error(`Falha ao extrair dados do contrato via N8N (operação: ${operationId}):`, error);
      await this.handleExtractionError(operationId, error.message);
    }
  }

  private async handleExtractionSuccess(operationId: string, data: any): Promise<void> {
    try {
      // Salvar dados extraídos
      await this.prisma.rentalContractData.upsert({
        where: { rentalRequestId: operationId },
        create: {
          rentalRequestId: operationId,
          propertyAddress: data.propertyAddress,
          landlordName: data.landlordName,
          tenantName: data.tenantName,
          landlordDocument: data.landlordDocument,
          tenantDocument: data.tenantDocument,
          rentalGuarantee: data.rentalGuarantee,
          contractTerm: data.contractTerm,
          startDate: data.startDate ? new Date(data.startDate) : null,
          endDate: data.endDate ? new Date(data.endDate) : null,
          propertyRegistry: data.propertyRegistry,
          extractedData: data,
        },
        update: {
          propertyAddress: data.propertyAddress,
          landlordName: data.landlordName,
          tenantName: data.tenantName,
          landlordDocument: data.landlordDocument,
          tenantDocument: data.tenantDocument,
          rentalGuarantee: data.rentalGuarantee,
          contractTerm: data.contractTerm,
          startDate: data.startDate ? new Date(data.startDate) : null,
          endDate: data.endDate ? new Date(data.endDate) : null,
          propertyRegistry: data.propertyRegistry,
          extractedData: data,
        },
      });

      // Atualizar status da operação
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operationId },
        data: { currentStatus: RentalAdvanceStatus.PDF_EXTRACTED },
      });

      // Adicionar log de status
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: operationId,
          status: RentalAdvanceStatus.PDF_EXTRACTED,
        },
      });

      this.logger.log(`Extração concluída com sucesso para operação: ${operationId}`);
    } catch (error) {
      this.logger.error(`Erro ao salvar dados extraídos para operação ${operationId}:`, error);
    }
  }

  private async handleExtractionError(operationId: string, errorMessage: string): Promise<void> {
    try {
      // Atualizar status para falha na extração
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operationId },
        data: { currentStatus: RentalAdvanceStatus.REJECTED },
      });

      // Adicionar log de status com erro
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: operationId,
          status: RentalAdvanceStatus.REJECTED,
        },
      });

      this.logger.log(`Operação ${operationId} marcada como rejeitada devido a erro na extração`);
    } catch (error) {
      this.logger.error(`Erro ao atualizar status de falha para operação ${operationId}:`, error);
    }
  }

  requestProposal(data: {
    operationId: string;
    rentAmount: number;
    monthsToAdvance: number;
    realEstateId: string;
    userId: string;
    extractedData?: any;
  }): void {
    if (!this.baseWebhookUrl) {
      this.logger.error('N8N webhook URL não configurada');
      return;
    }

    // Executa de forma assíncrona sem bloquear
    this.processProposalRequest(data);
  }

  private async processProposalRequest(data: {
    operationId: string;
    rentAmount: number;
    monthsToAdvance: number;
    realEstateId: string;
    userId: string;
    extractedData?: any;
  }): Promise<void> {
    try {
      this.logger.log(
        `Solicitando proposta para operação: ${data.operationId}`,
      );

      const response: AxiosResponse = await axios.post(
        `${this.baseWebhookUrl}/request-proposal`,
        {
          ...data,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 30000, // 30 segundos para proposta
          headers: {
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      if (!response.data) {
        throw new Error('Resposta vazia do N8N para proposta');
      }

      // Validação da resposta da proposta
      const proposal = response.data;
      if (
        !proposal.proposalAmount ||
        !proposal.monthlyRentOffer ||
        !proposal.proposedMonths
      ) {
        throw new Error('Dados da proposta incompletos');
      }

      // Processar resposta e atualizar banco de dados
      await this.handleProposalSuccess(data.operationId, proposal);

    } catch (error) {
      this.logger.error(`Falha ao solicitar proposta via N8N (operação: ${data.operationId}):`, error);
      await this.handleProposalError(data.operationId, error.message);
    }
  }

  private async handleProposalSuccess(operationId: string, proposal: any): Promise<void> {
    try {
      // Salvar proposta
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operationId },
        data: {
          currentStatus: RentalAdvanceStatus.PROPOSAL_SENT,
          proposalAmount: proposal.proposalAmount,
          monthlyRentOffer: proposal.monthlyRentOffer,
          proposedMonths: proposal.proposedMonths,
        },
      });

      // Adicionar log de status
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: operationId,
          status: RentalAdvanceStatus.PROPOSAL_SENT,
        },
      });

      this.logger.log(`Proposta gerada com sucesso para operação: ${operationId}`);
    } catch (error) {
      this.logger.error(`Erro ao salvar proposta para operação ${operationId}:`, error);
    }
  }

  private async handleProposalError(operationId: string, errorMessage: string): Promise<void> {
    try {
      // Atualizar status para falha na geração da proposta
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operationId },
        data: { currentStatus: RentalAdvanceStatus.REJECTED },
      });

      // Adicionar log de status com erro
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: operationId,
          status: RentalAdvanceStatus.REJECTED,
        },
      });

      this.logger.log(`Operação ${operationId} marcada como rejeitada devido a erro na geração da proposta`);
    } catch (error) {
      this.logger.error(`Erro ao atualizar status de falha para operação ${operationId}:`, error);
    }
  }

  async notifyStatusChange(
    operationId: string,
    newStatus: string,
    additionalData?: any,
  ): Promise<void> {
    if (!this.baseWebhookUrl) {
      this.logger.warn(
        'N8N webhook URL não configurada - notificação não enviada',
      );
      return;
    }

    try {
      this.logger.log(
        `Notificando mudança de status: ${operationId} -> ${newStatus}`,
      );

      await axios.post(
        `${this.baseWebhookUrl}/status-notification`,
        {
          operationId,
          status: newStatus,
          timestamp: new Date().toISOString(),
          ...additionalData,
        },
        {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      this.logger.log('Notificação de status enviada com sucesso');
    } catch (error) {
      // Não lançar erro aqui para não quebrar o fluxo principal
      this.logger.error('Falha ao notificar mudança de status via N8N:', error);
    }
  }

  async sendDocumentNotification(
    operationId: string,
    documentType: string,
    documentUrl: string,
  ): Promise<void> {
    if (!this.baseWebhookUrl) {
      this.logger.warn(
        'N8N webhook URL não configurada - notificação não enviada',
      );
      return;
    }

    try {
      this.logger.log(
        `Notificando novo documento: ${operationId} -> ${documentType}`,
      );

      await axios.post(
        `${this.baseWebhookUrl}/document-notification`,
        {
          operationId,
          documentType,
          documentUrl,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 10000,
          headers: { 
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
           },
        },
      );

      this.logger.log('Notificação de documento enviada com sucesso');
    } catch (error) {
      // Não lançar erro aqui para não quebrar o fluxo principal
      this.logger.error('Falha ao notificar documento via N8N:', error);
    }
  }
}
