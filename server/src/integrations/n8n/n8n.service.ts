import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { PrismaService } from '../../prisma.service';
import { DriveService } from '../drive/drive.service';
import { RentalAdvanceStatus } from '../../rental-advance/enums/rental-status.enum';

@Injectable()
export class N8nService {
  private readonly logger = new Logger(N8nService.name);
  private readonly baseWebhookUrl = process.env.N8N_WEBHOOK_URL;

  constructor(
    private readonly prisma: PrismaService,
    private readonly driveService: DriveService,
  ) {
    if (!this.baseWebhookUrl) {
      this.logger.error('N8N_WEBHOOK_URL não configurada');
    }
  }

  async sendWhatsappCode(phone: string, code: string): Promise<void> {
    if (!this.baseWebhookUrl) {
      throw new InternalServerErrorException('N8N webhook URL não configurada');
    }

    try {
      this.logger.log(
        `Enviando código WhatsApp para: ${phone.substring(0, 4)}****`,
      );

      const response = await axios.post(
        `${this.baseWebhookUrl}/whatsapp-code`,
        {
          phone,
          code,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      if (response.status !== 200) {
        throw new Error(`N8N retornou status ${response.status}`);
      }

      this.logger.log('Código WhatsApp enviado com sucesso');
    } catch (error) {
      this.logger.error('Falha ao enviar código via N8N:', error);
      throw new InternalServerErrorException(
        'Falha ao enviar código de verificação. Tente novamente.', + this.baseWebhookUrl + '/whatsapp-code',
      );
    }
  }

  sendContractForExtraction(
    operationId: string,
    contractPdf: Express.Multer.File,
  ): void {
    if (!this.baseWebhookUrl) {
      this.logger.error('N8N webhook URL não configurada');
      return;
    }

    // Executa de forma assíncrona sem bloquear
    this.processContractExtraction(operationId, contractPdf);
  }

  private async processContractExtraction(
    operationId: string,
    contractPdf: Express.Multer.File,
  ): Promise<void> {
    try {
      this.logger.log(
        `Enviando contrato para extração: ${contractPdf.originalname} (operação: ${operationId})`,
      );

      // Debug: verificar dados do arquivo antes de enviar para N8N
      this.logger.log(`Arquivo para N8N - Nome: ${contractPdf.originalname}, Tamanho: ${contractPdf.buffer.length} bytes, MIME: ${contractPdf.mimetype}`);

      // Verificar se é um PDF válido
      const bufferStart = contractPdf.buffer.subarray(0, 10).toString();
      this.logger.log(`Buffer start para N8N: ${bufferStart}`);

      if (!bufferStart.startsWith('%PDF')) {
        this.logger.warn('AVISO: Buffer enviado para N8N não parece ser um PDF válido');
      }

      const formData = new FormData();
      formData.append(
        'file',
        new Blob([contractPdf.buffer]),
        contractPdf.originalname,
      );
      formData.append('mimeType', contractPdf.mimetype);
      formData.append('operationId', operationId);
      formData.append('timestamp', new Date().toISOString());

      const response: AxiosResponse = await axios.post(
        `${this.baseWebhookUrl}/extract-contract`,
        formData,
        {
          timeout: 60000, // 60 segundos para extração
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      // Debug: log da resposta do N8N
      this.logger.log(`Resposta do N8N - Status: ${response.status}, Data: ${JSON.stringify(response.data)}`);

      if (!response.data) {
        throw new Error('Resposta vazia do N8N');
      }

      // Processar resposta e atualizar banco de dados
      await this.handleExtractionSuccess(operationId, response.data);

    } catch (error) {
      this.logger.error(`Falha ao extrair dados do contrato via N8N (operação: ${operationId}):`, error);
      await this.handleExtractionError(operationId, error.message);
    }
  }

  private async handleExtractionSuccess(operationId: string, data: any): Promise<void> {
    try {
      // Salvar dados extraídos
      await this.prisma.rentalContractData.upsert({
        where: { rentalRequestId: operationId },
        create: {
          rentalRequestId: operationId,
          propertyAddress: data.propertyAddress,
          landlordName: data.landlordName,
          tenantName: data.tenantName,
          landlordDocument: data.landlordDocument,
          tenantDocument: data.tenantDocument,
          rentalGuarantee: data.rentalGuarantee,
          contractTerm: data.contractTerm,
          startDate: data.startDate ? new Date(data.startDate) : null,
          endDate: data.endDate ? new Date(data.endDate) : null,
          propertyRegistry: data.propertyRegistry,
          extractedData: data,
        },
        update: {
          propertyAddress: data.propertyAddress,
          landlordName: data.landlordName,
          tenantName: data.tenantName,
          landlordDocument: data.landlordDocument,
          tenantDocument: data.tenantDocument,
          rentalGuarantee: data.rentalGuarantee,
          contractTerm: data.contractTerm,
          startDate: data.startDate ? new Date(data.startDate) : null,
          endDate: data.endDate ? new Date(data.endDate) : null,
          propertyRegistry: data.propertyRegistry,
          extractedData: data,
        },
      });

      // Atualizar status da operação
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operationId },
        data: { currentStatus: RentalAdvanceStatus.PDF_EXTRACTED },
      });

      // Adicionar log de status
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: operationId,
          status: RentalAdvanceStatus.PDF_EXTRACTED,
        },
      });

      this.logger.log(`Extração concluída com sucesso para operação: ${operationId}`);
    } catch (error) {
      this.logger.error(`Erro ao salvar dados extraídos para operação ${operationId}:`, error);
    }
  }

  private async handleExtractionError(operationId: string, errorMessage: string): Promise<void> {
    try {
      this.logger.log(`Iniciando exclusão da operação ${operationId} devido a erro na extração: ${errorMessage}`);

      // Buscar a operação para obter informações necessárias
      const operation = await this.prisma.rentalAdvanceRequest.findUnique({
        where: { id: operationId },
        select: {
          id: true,
          contractPdfUrl: true,
          identityDocUrl: true
        },
      });

      if (!operation) {
        this.logger.warn(`Operação ${operationId} não encontrada para exclusão`);
        return;
      }

      // Deletar arquivos do Google Drive se existirem
      if (operation.contractPdfUrl) {
        try {
          await this.deleteFileFromDrive(operation.contractPdfUrl);
          this.logger.log(`Arquivo de contrato deletado do Drive para operação ${operationId}`);
        } catch (error) {
          this.logger.error(`Erro ao deletar arquivo de contrato do Drive:`, error);
        }
      }

      if (operation.identityDocUrl) {
        try {
          await this.deleteFileFromDrive(operation.identityDocUrl);
          this.logger.log(`Documento de identidade deletado do Drive para operação ${operationId}`);
        } catch (error) {
          this.logger.error(`Erro ao deletar documento de identidade do Drive:`, error);
        }
      }

      // Deletar registros relacionados primeiro (devido a foreign keys)
      await this.prisma.rentalRequestStatusLog.deleteMany({
        where: { rentalRequestId: operationId },
      });

      await this.prisma.rentalContractData.deleteMany({
        where: { rentalRequestId: operationId },
      });

      // Note: No separate proposal table exists - proposal data is stored in RentalAdvanceRequest

      // Deletar a operação principal
      await this.prisma.rentalAdvanceRequest.delete({
        where: { id: operationId },
      });

      this.logger.log(`Operação ${operationId} e todos os dados relacionados foram deletados devido a erro na extração`);
    } catch (error) {
      this.logger.error(`Erro ao deletar operação ${operationId} após falha na extração:`, error);
      // Em caso de erro na exclusão, marcar como cancelada como fallback
      try {
        await this.prisma.rentalAdvanceRequest.update({
          where: { id: operationId },
          data: { currentStatus: RentalAdvanceStatus.CANCELLED },
        });
        this.logger.log(`Operação ${operationId} marcada como cancelada como fallback`);
      } catch (fallbackError) {
        this.logger.error(`Erro no fallback para operação ${operationId}:`, fallbackError);
      }
    }
  }

  private async deleteFileFromDrive(fileUrl: string): Promise<void> {
    try {
      // Extrair file ID da URL do Google Drive
      const fileIdMatch = fileUrl.match(/\/file\/d\/([a-zA-Z0-9-_]+)/);
      if (!fileIdMatch) {
        this.logger.warn(`Não foi possível extrair file ID da URL: ${fileUrl}`);
        return;
      }

      const fileId = fileIdMatch[1];

      await this.driveService.deleteFile(fileId);
      this.logger.log(`Arquivo ${fileId} deletado do Google Drive`);
    } catch (error) {
      this.logger.error(`Erro ao deletar arquivo do Drive:`, error);
      throw error;
    }
  }

  requestProposal(data: {
    operationId: string;
    rentAmount: number;
    monthsToAdvance: number;
    realEstateId: string;
    userId: string;
    extractedData?: any;
  }): void {
    if (!this.baseWebhookUrl) {
      this.logger.error('N8N webhook URL não configurada');
      return;
    }

    // Executa de forma assíncrona sem bloquear
    this.processProposalRequest(data);
  }

  private async processProposalRequest(data: {
    operationId: string;
    rentAmount: number;
    monthsToAdvance: number;
    realEstateId: string;
    userId: string;
    extractedData?: any;
  }): Promise<void> {
    try {
      this.logger.log(
        `Solicitando proposta para operação: ${data.operationId}`,
      );

      const response: AxiosResponse = await axios.post(
        `${this.baseWebhookUrl}/request-proposal`,
        {
          ...data,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 30000, // 30 segundos para proposta
          headers: {
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      if (!response.data) {
        throw new Error('Resposta vazia do N8N para proposta');
      }

      // Validação da resposta da proposta
      const proposal = response.data;
      if (
        !proposal.proposalAmount ||
        !proposal.monthlyRentOffer ||
        !proposal.proposedMonths
      ) {
        throw new Error('Dados da proposta incompletos');
      }

      // Processar resposta e atualizar banco de dados
      await this.handleProposalSuccess(data.operationId, proposal);

    } catch (error) {
      this.logger.error(`Falha ao solicitar proposta via N8N (operação: ${data.operationId}):`, error);
      await this.handleProposalError(data.operationId, error.message);
    }
  }

  private async handleProposalSuccess(operationId: string, proposal: any): Promise<void> {
    try {
      // Salvar proposta
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operationId },
        data: {
          currentStatus: RentalAdvanceStatus.PROPOSAL_SENT,
          proposalAmount: proposal.proposalAmount,
          monthlyRentOffer: proposal.monthlyRentOffer,
          proposedMonths: proposal.proposedMonths,
        },
      });

      // Adicionar log de status
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: operationId,
          status: RentalAdvanceStatus.PROPOSAL_SENT,
        },
      });

      this.logger.log(`Proposta gerada com sucesso para operação: ${operationId}`);
    } catch (error) {
      this.logger.error(`Erro ao salvar proposta para operação ${operationId}:`, error);
    }
  }

  private async handleProposalError(operationId: string, errorMessage: string): Promise<void> {
    try {
      this.logger.log(`Iniciando exclusão da operação ${operationId} devido a erro na geração da proposta: ${errorMessage}`);

      // Buscar a operação para obter informações necessárias
      const operation = await this.prisma.rentalAdvanceRequest.findUnique({
        where: { id: operationId },
        select: {
          id: true,
          contractPdfUrl: true,
          identityDocUrl: true
        },
      });

      if (!operation) {
        this.logger.warn(`Operação ${operationId} não encontrada para exclusão`);
        return;
      }

      // Deletar arquivos do Google Drive se existirem
      if (operation.contractPdfUrl) {
        try {
          await this.deleteFileFromDrive(operation.contractPdfUrl);
          this.logger.log(`Arquivo de contrato deletado do Drive para operação ${operationId}`);
        } catch (error) {
          this.logger.error(`Erro ao deletar arquivo de contrato do Drive:`, error);
        }
      }

      if (operation.identityDocUrl) {
        try {
          await this.deleteFileFromDrive(operation.identityDocUrl);
          this.logger.log(`Documento de identidade deletado do Drive para operação ${operationId}`);
        } catch (error) {
          this.logger.error(`Erro ao deletar documento de identidade do Drive:`, error);
        }
      }

      // Deletar registros relacionados primeiro (devido a foreign keys)
      await this.prisma.rentalRequestStatusLog.deleteMany({
        where: { rentalRequestId: operationId },
      });

      await this.prisma.rentalContractData.deleteMany({
        where: { rentalRequestId: operationId },
      });

      // Deletar a operação principal
      await this.prisma.rentalAdvanceRequest.delete({
        where: { id: operationId },
      });

      this.logger.log(`Operação ${operationId} e todos os dados relacionados foram deletados devido a erro na geração da proposta`);
    } catch (error) {
      this.logger.error(`Erro ao deletar operação ${operationId} após falha na geração da proposta:`, error);
      // Em caso de erro na exclusão, marcar como cancelada como fallback
      try {
        await this.prisma.rentalAdvanceRequest.update({
          where: { id: operationId },
          data: { currentStatus: RentalAdvanceStatus.CANCELLED },
        });
        this.logger.log(`Operação ${operationId} marcada como cancelada como fallback`);
      } catch (fallbackError) {
        this.logger.error(`Erro no fallback para operação ${operationId}:`, fallbackError);
      }
    }
  }

  async notifyStatusChange(
    operationId: string,
    newStatus: string,
    additionalData?: any,
  ): Promise<void> {
    if (!this.baseWebhookUrl) {
      this.logger.warn(
        'N8N webhook URL não configurada - notificação não enviada',
      );
      return;
    }

    try {
      this.logger.log(
        `Notificando mudança de status: ${operationId} -> ${newStatus}`,
      );

      await axios.post(
        `${this.baseWebhookUrl}/status-notification`,
        {
          operationId,
          status: newStatus,
          timestamp: new Date().toISOString(),
          ...additionalData,
        },
        {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      this.logger.log('Notificação de status enviada com sucesso');
    } catch (error) {
      // Não lançar erro aqui para não quebrar o fluxo principal
      this.logger.error('Falha ao notificar mudança de status via N8N:', error);
    }
  }

  /**
   * Envia arquivo PDF diretamente para N8N para upload no Drive
   */
  async uploadFileViaN8N(
    operationId: string,
    file: Express.Multer.File,
    fileType: 'contract' | 'identity'
  ): Promise<string> {
    if (!this.baseWebhookUrl) {
      throw new Error('N8N webhook URL não configurada');
    }

    try {
      this.logger.log(
        `Enviando arquivo ${file.originalname} para N8N (operação: ${operationId}, tipo: ${fileType})`,
      );

      // Debug: verificar dados do arquivo
      this.logger.log(`Arquivo para upload via N8N - Nome: ${file.originalname}, Tamanho: ${file.buffer.length} bytes, MIME: ${file.mimetype}`);

      const formData = new FormData();
      formData.append(
        'file',
        new Blob([file.buffer]),
        file.originalname,
      );
      formData.append('mimeType', file.mimetype);
      formData.append('operationId', operationId);
      formData.append('fileType', fileType);
      formData.append('timestamp', new Date().toISOString());

      const response: AxiosResponse = await axios.post(
        `${this.baseWebhookUrl}/upload-file`,
        formData,
        {
          timeout: 60000, // 60 segundos para upload
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
          },
        },
      );

      // Debug: log da resposta do N8N
      this.logger.log(`Resposta do upload N8N - Status: ${response.status}, Data: ${JSON.stringify(response.data)}`);

      if (!response.data || !response.data.fileUrl) {
        throw new Error('N8N não retornou URL do arquivo');
      }

      this.logger.log(`Arquivo ${file.originalname} enviado com sucesso via N8N: ${response.data.fileUrl}`);
      return response.data.fileUrl;

    } catch (error) {
      this.logger.error(`Erro ao enviar arquivo via N8N para operação ${operationId}:`, error);
      throw new Error(`Falha no upload via N8N: ${error.message}`);
    }
  }

  async sendDocumentNotification(
    operationId: string,
    documentType: string,
    documentUrl: string,
  ): Promise<void> {
    if (!this.baseWebhookUrl) {
      this.logger.warn(
        'N8N webhook URL não configurada - notificação não enviada',
      );
      return;
    }

    try {
      this.logger.log(
        `Notificando novo documento: ${operationId} -> ${documentType}`,
      );

      await axios.post(
        `${this.baseWebhookUrl}/document-notification`,
        {
          operationId,
          documentType,
          documentUrl,
          timestamp: new Date().toISOString(),
        },
        {
          timeout: 10000,
          headers: { 
            'Content-Type': 'application/json',
            'x-backend-auth': `${process.env.N8N_AUTH_HEADER}`
           },
        },
      );

      this.logger.log('Notificação de documento enviada com sucesso');
    } catch (error) {
      // Não lançar erro aqui para não quebrar o fluxo principal
      this.logger.error('Falha ao notificar documento via N8N:', error);
    }
  }
}
