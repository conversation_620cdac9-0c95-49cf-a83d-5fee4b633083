export enum RentalAdvanceStatus {
  CREATED = 'created',
  PDF_UPLOADED = 'pdf_uploaded',
  PDF_EXTRACTED = 'pdf_extracted',
  DATA_CONFIRMED = 'data_confirmed',
  PENDING_PROPOSAL = 'pending_proposal',
  PROPOSAL_SENT = 'proposal_sent',
  DOCS_UPLOADED = 'docs_uploaded',
  AWAITING_REVIEW = 'awaiting_review',
  APPROVED = 'approved',
  CANCELLED = 'cancelled',
}

export enum PixKeyType {
  CPF = 'cpf',
  EMAIL = 'email',
  PHONE = 'phone',
  RANDOM = 'random',
}
